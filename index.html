<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ultra-Realistic 3D Tile Visualizer</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --primary: #3b82f6;
        --primary-dark: #2563eb;
        --secondary: #8b5cf6;
        --background: #0f172a;
        --surface: #1e293b;
        --surface-light: #334155;
        --text: #e2e8f0;
        --text-secondary: #94a3b8;
        --border: #334155;
        --success: #10b981;
        --warning: #f59e0b;
        --error: #ef4444;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        background-color: var(--background);
        color: var(--text);
        line-height: 1.6;
        overflow: hidden;
      }

      .container {
        display: flex;
        height: 100vh;
      }

      .sidebar {
        width: 380px;
        background: var(--surface);
        padding: 1.5rem;
        overflow-y: auto;
        z-index: 10;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
      }

      .logo {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        background: linear-gradient(
          135deg,
          var(--primary) 0%,
          var(--secondary) 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .section {
        margin-bottom: 2rem;
      }

      .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text);
      }

      .upload-area {
        border: 2px dashed var(--border);
        border-radius: 0.75rem;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(59, 130, 246, 0.05);
      }

      .upload-area:hover {
        border-color: var(--primary);
        background: rgba(59, 130, 246, 0.1);
      }

      .upload-area.dragover {
        border-color: var(--primary);
        background: rgba(59, 130, 246, 0.15);
      }

      .upload-icon {
        font-size: 2.5rem;
        margin-bottom: 0.75rem;
      }

      .upload-text {
        color: var(--text-secondary);
        margin-bottom: 1rem;
        font-size: 0.9rem;
      }

      .file-input {
        display: none;
      }

      .btn {
        background: var(--primary);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        width: 100%;
      }

      .btn:hover {
        background: var(--primary-dark);
      }

      .btn-secondary {
        background: var(--surface-light);
      }

      .btn-secondary:hover {
        background: #475569;
      }

      .room-selector {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
      }

      .room-option {
        background: var(--surface-light);
        border: 2px solid transparent;
        border-radius: 0.75rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
      }

      .room-option:hover {
        border-color: var(--primary);
        transform: translateY(-2px);
      }

      .room-option.active {
        border-color: var(--primary);
        background: rgba(59, 130, 246, 0.1);
      }

      .room-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }

      .room-name {
        font-weight: 500;
      }

      .tile-preview {
        margin-top: 1rem;
        border-radius: 0.5rem;
        overflow: hidden;
        height: 150px;
        background: var(--surface-light);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tile-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
      }

      .controls {
        margin-top: 2rem;
      }

      .control-group {
        margin-bottom: 1.5rem;
      }

      .control-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        font-size: 0.9rem;
        color: var(--text-secondary);
      }

      .slider {
        width: 100%;
        height: 6px;
        border-radius: 3px;
        background: var(--surface-light);
        outline: none;
        -webkit-appearance: none;
      }

      .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: var(--primary);
        cursor: pointer;
      }

      .slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: var(--primary);
        cursor: pointer;
        border: none;
      }

      .slider-value {
        display: inline-block;
        margin-left: 0.5rem;
        font-weight: 600;
        color: var(--primary);
      }

      .viewer {
        flex: 1;
        position: relative;
        background: #000;
      }

      #canvas-container {
        width: 100%;
        height: 100%;
      }

      .viewer-controls {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 1rem;
        background: rgba(30, 41, 59, 0.8);
        padding: 1rem;
        border-radius: 0.75rem;
        backdrop-filter: blur(10px);
      }

      .viewer-btn {
        background: var(--surface-light);
        border: none;
        color: var(--text);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .viewer-btn:hover {
        background: var(--primary);
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top-color: var(--primary);
        animation: spin 1s ease-in-out infinite;
        margin: 0 auto 1rem;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .toast {
        position: fixed;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        background: var(--surface);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .toast.show {
        opacity: 1;
      }

      .pattern-selector {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
      }

      .pattern-option {
        background: var(--surface-light);
        border: 2px solid transparent;
        border-radius: 0.5rem;
        padding: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-size: 0.85rem;
      }

      .pattern-option:hover {
        border-color: var(--primary);
      }

      .pattern-option.active {
        border-color: var(--primary);
        background: rgba(59, 130, 246, 0.1);
      }

      .application-selector {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
      }

      .application-option {
        flex: 1;
        background: var(--surface-light);
        border: 2px solid transparent;
        border-radius: 0.5rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
      }

      .application-option:hover {
        border-color: var(--primary);
      }

      .application-option.active {
        border-color: var(--primary);
        background: rgba(59, 130, 246, 0.1);
      }

      .application-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }

      .quality-selector {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
      }

      .quality-option {
        flex: 1;
        background: var(--surface-light);
        border: 2px solid transparent;
        border-radius: 0.5rem;
        padding: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-size: 0.85rem;
      }

      .quality-option:hover {
        border-color: var(--primary);
      }

      .quality-option.active {
        border-color: var(--primary);
        background: rgba(59, 130, 246, 0.1);
      }

      @media (max-width: 768px) {
        .container {
          flex-direction: column;
        }

        .sidebar {
          width: 100%;
          height: auto;
          max-height: 40vh;
        }

        .viewer {
          height: 60vh;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <aside class="sidebar">
        <h1 class="logo">Ultra-Realistic 3D Tile Visualizer</h1>

        <div class="section">
          <h2 class="section-title">Upload Tile Image</h2>
          <div class="upload-area" id="uploadArea">
            <div class="upload-icon">🖼️</div>
            <p class="upload-text">Drag & drop or click to browse</p>
            <button class="btn">Choose Image</button>
            <input
              type="file"
              class="file-input"
              id="fileInput"
              accept="image/*"
            />
          </div>
          <div class="tile-preview" id="tilePreview">
            <span style="color: var(--text-secondary)">No tile selected</span>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Select Room</h2>
          <div class="room-selector">
            <div class="room-option active" data-room="kitchen">
              <div class="room-icon">🍳</div>
              <div class="room-name">Modern Kitchen</div>
            </div>
            <div class="room-option" data-room="bathroom">
              <div class="room-icon">🚿</div>
              <div class="room-name">Luxury Bathroom</div>
            </div>
            <div class="room-option" data-room="living">
              <div class="room-icon">🛋️</div>
              <div class="room-name">Living Room</div>
            </div>
            <div class="room-option" data-room="bedroom">
              <div class="room-icon">🛏️</div>
              <div class="room-name">Master Bedroom</div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Apply To</h2>
          <div class="application-selector">
            <div class="application-option active" data-application="floor">
              <div class="application-icon">🏢</div>
              <div>Floor</div>
            </div>
            <div class="application-option" data-application="wall">
              <div class="application-icon">🧱</div>
              <div>Wall</div>
            </div>
            <div class="application-option" data-application="both">
              <div class="application-icon">🏠</div>
              <div>Both</div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Tile Pattern</h2>
          <div class="pattern-selector">
            <div class="pattern-option active" data-pattern="grid">Grid</div>
            <div class="pattern-option" data-pattern="diagonal">Diagonal</div>
            <div class="pattern-option" data-pattern="herringbone">
              Herringbone
            </div>
            <div class="pattern-option" data-pattern="basketweave">
              Basket Weave
            </div>
            <div class="pattern-option" data-pattern="hexagon">Hexagon</div>
            <div class="pattern-option" data-pattern="brick">Brick</div>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Render Quality</h2>
          <div class="quality-selector">
            <div class="quality-option" data-quality="low">Fast</div>
            <div class="quality-option active" data-quality="medium">
              Balanced
            </div>
            <div class="quality-option" data-quality="high">Ultra</div>
          </div>
        </div>

        <div class="section controls">
          <h2 class="section-title">Adjustments</h2>

          <div class="control-group">
            <label class="control-label">
              Tile Size
              <span class="slider-value" id="tileSizeValue">1.0</span>
            </label>
            <input
              type="range"
              class="slider"
              id="tileSize"
              min="0.5"
              max="2"
              step="0.1"
              value="1"
            />
          </div>

          <div class="control-group">
            <label class="control-label">
              Grout Width
              <span class="slider-value" id="groutWidthValue">0.02</span>
            </label>
            <input
              type="range"
              class="slider"
              id="groutWidth"
              min="0"
              max="0.1"
              step="0.01"
              value="0.02"
            />
          </div>

          <div class="control-group">
            <label class="control-label"> Grout Color </label>
            <input
              type="color"
              id="groutColor"
              value="#cccccc"
              style="
                width: 100%;
                height: 40px;
                border: none;
                border-radius: 0.5rem;
                cursor: pointer;
              "
            />
          </div>

          <div class="control-group">
            <label class="control-label">
              Wall Height
              <span class="slider-value" id="wallHeightValue">3.0</span>
            </label>
            <input
              type="range"
              class="slider"
              id="wallHeight"
              min="2.5"
              max="4"
              step="0.1"
              value="3"
            />
          </div>

          <div class="control-group">
            <label class="control-label">
              Room Lighting
              <span class="slider-value" id="lightingValue">1.0</span>
            </label>
            <input
              type="range"
              class="slider"
              id="lighting"
              min="0.3"
              max="1.5"
              step="0.1"
              value="1"
            />
          </div>

          <button
            class="btn btn-secondary"
            id="resetView"
            style="margin-top: 1rem"
          >
            Reset View
          </button>
        </div>
      </aside>

      <main class="viewer">
        <div id="canvas-container"></div>

        <div class="viewer-controls">
          <button class="viewer-btn" id="zoomIn" title="Zoom In">+</button>
          <button class="viewer-btn" id="zoomOut" title="Zoom Out">-</button>
          <button class="viewer-btn" id="rotateLeft" title="Rotate Left">
            ↶
          </button>
          <button class="viewer-btn" id="rotateRight" title="Rotate Right">
            ↷
          </button>
          <button class="viewer-btn" id="toggleFullscreen" title="Fullscreen">
            ⛶
          </button>
        </div>

        <div class="loading" id="loading">
          <div class="loading-spinner"></div>
          <p>Loading ultra-realistic environment...</p>
        </div>
      </main>
    </div>

    <div class="toast" id="toast"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script>
      // Global variables
      let scene, camera, renderer, controls;
      let tileTexture = null;
      let currentRoom = "kitchen";
      let currentPattern = "grid";
      let currentApplication = "floor";
      let currentQuality = "medium";
      let floorMesh,
        wallMeshes = [],
        ceilingMesh;
      let roomGroup;
      let lights = {};
      let isFullscreen = false;
      let clock = new THREE.Clock();

      // DOM elements
      const uploadArea = document.getElementById("uploadArea");
      const fileInput = document.getElementById("fileInput");
      const tilePreview = document.getElementById("tilePreview");
      const roomOptions = document.querySelectorAll(".room-option");
      const applicationOptions = document.querySelectorAll(
        ".application-option"
      );
      const patternOptions = document.querySelectorAll(".pattern-option");
      const qualityOptions = document.querySelectorAll(".quality-option");
      const tileSizeSlider = document.getElementById("tileSize");
      const tileSizeValue = document.getElementById("tileSizeValue");
      const groutWidthSlider = document.getElementById("groutWidth");
      const groutWidthValue = document.getElementById("groutWidthValue");
      const groutColorInput = document.getElementById("groutColor");
      const wallHeightSlider = document.getElementById("wallHeight");
      const wallHeightValue = document.getElementById("wallHeightValue");
      const lightingSlider = document.getElementById("lighting");
      const lightingValue = document.getElementById("lightingValue");
      const resetViewBtn = document.getElementById("resetView");
      const zoomInBtn = document.getElementById("zoomIn");
      const zoomOutBtn = document.getElementById("zoomOut");
      const rotateLeftBtn = document.getElementById("rotateLeft");
      const rotateRightBtn = document.getElementById("rotateRight");
      const toggleFullscreenBtn = document.getElementById("toggleFullscreen");
      const loading = document.getElementById("loading");
      const toast = document.getElementById("toast");

      // Initialize Three.js
      function init() {
        // Create scene
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0xf5f5f5);
        scene.fog = new THREE.Fog(0xf5f5f5, 10, 50);

        // Create camera
        const container = document.getElementById("canvas-container");
        camera = new THREE.PerspectiveCamera(
          35,
          container.clientWidth / container.clientHeight,
          0.1,
          1000
        );
        camera.position.set(10, 8, 10);
        camera.lookAt(0, 0, 0);

        // Create renderer
        renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true,
        });
        renderer.setSize(container.clientWidth, container.clientHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        renderer.toneMapping = THREE.ACESFilmicToneMapping;
        renderer.toneMappingExposure = 1.0;
        renderer.outputEncoding = THREE.sRGBEncoding;
        renderer.physicallyCorrectLights = true;
        container.appendChild(renderer.domElement);

        // Add orbit controls
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.screenSpacePanning = false;
        controls.minDistance = 3;
        controls.maxDistance = 30;
        controls.maxPolarAngle = Math.PI / 2.1;

        // Setup lighting
        setupLighting();

        // Create initial room
        createRoom(currentRoom);

        // Hide loading screen
        setTimeout(() => {
          loading.style.display = "none";
        }, 2000);

        // Handle window resize
        window.addEventListener("resize", onWindowResize);

        // Start animation loop
        animate();
      }

      function setupLighting() {
        // Environment map for realistic reflections
        const pmremGenerator = new THREE.PMREMGenerator(renderer);
        pmremGenerator.compileEquirectangularShader();

        // Ambient light for base illumination
        lights.ambient = new THREE.AmbientLight(0xffffff, 0.3);
        scene.add(lights.ambient);

        // Main directional light (sun)
        lights.directional = new THREE.DirectionalLight(0xffffff, 1.0);
        lights.directional.position.set(15, 30, 10);
        lights.directional.castShadow = true;
        lights.directional.shadow.mapSize.width = 4096;
        lights.directional.shadow.mapSize.height = 4096;
        lights.directional.shadow.camera.near = 0.5;
        lights.directional.shadow.camera.far = 100;
        lights.directional.shadow.camera.left = -20;
        lights.directional.shadow.camera.right = 20;
        lights.directional.shadow.camera.top = 20;
        lights.directional.shadow.camera.bottom = -20;
        lights.directional.shadow.bias = -0.0001;
        scene.add(lights.directional);

        // Fill light for softer shadows
        lights.fill = new THREE.DirectionalLight(0xffffff, 0.4);
        lights.fill.position.set(-10, 20, -10);
        scene.add(lights.fill);

        // Point lights for interior lighting
        lights.point1 = new THREE.PointLight(0xffd700, 0.8, 15);
        lights.point1.position.set(0, 4, 0);
        lights.point1.castShadow = true;
        lights.point1.shadow.mapSize.width = 1024;
        lights.point1.shadow.mapSize.height = 1024;
        scene.add(lights.point1);

        lights.point2 = new THREE.PointLight(0xffd700, 0.6, 12);
        lights.point2.position.set(5, 3, 5);
        lights.point2.castShadow = true;
        scene.add(lights.point2);

        lights.point3 = new THREE.PointLight(0xffd700, 0.6, 12);
        lights.point3.position.set(-5, 3, 5);
        lights.point3.castShadow = true;
        scene.add(lights.point3);

        // Hemisphere light for natural ambient
        lights.hemisphere = new THREE.HemisphereLight(0x87ceeb, 0x8b7355, 0.4);
        scene.add(lights.hemisphere);
      }

      function updateQuality(quality) {
        currentQuality = quality;

        switch (quality) {
          case "low":
            renderer.shadowMap.enabled = false;
            renderer.antialias = false;
            lights.directional.shadow.mapSize.width = 1024;
            lights.directional.shadow.mapSize.height = 1024;
            break;
          case "medium":
            renderer.shadowMap.enabled = true;
            renderer.antialias = true;
            lights.directional.shadow.mapSize.width = 2048;
            lights.directional.shadow.mapSize.height = 2048;
            break;
          case "high":
            renderer.shadowMap.enabled = true;
            renderer.antialias = true;
            lights.directional.shadow.mapSize.width = 4096;
            lights.directional.shadow.mapSize.height = 4096;
            break;
        }
      }

      function createRoom(roomType) {
        // Remove existing room if any
        if (roomGroup) {
          scene.remove(roomGroup);
        }

        // Create new group for the room
        roomGroup = new THREE.Group();

        const wallHeight = parseFloat(wallHeightSlider.value);

        // Create floor
        const floorGeometry = new THREE.PlaneGeometry(15, 15);
        let floorMaterial;

        if (
          tileTexture &&
          (currentApplication === "floor" || currentApplication === "both")
        ) {
          floorMaterial = createTileMaterial(tileTexture, currentPattern);
        } else {
          // Default high-quality marble floor
          const floorTexture = createProceduralMarble();
          floorMaterial = new THREE.MeshStandardMaterial({
            map: floorTexture,
            roughness: 0.2,
            metalness: 0.1,
            normalMap: createNormalMap(),
            normalScale: new THREE.Vector2(0.8, 0.8),
          });
        }

        floorMesh = new THREE.Mesh(floorGeometry, floorMaterial);
        floorMesh.rotation.x = -Math.PI / 2;
        floorMesh.receiveShadow = true;
        roomGroup.add(floorMesh);

        // Create ceiling
        const ceilingGeometry = new THREE.PlaneGeometry(15, 15);
        const ceilingMaterial = new THREE.MeshStandardMaterial({
          color: 0xf8f8f8,
          roughness: 0.9,
          metalness: 0.1,
        });
        ceilingMesh = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
        ceilingMesh.rotation.x = Math.PI / 2;
        ceilingMesh.position.y = wallHeight;
        ceilingMesh.receiveShadow = true;
        roomGroup.add(ceilingMesh);

        // Create walls with realistic materials
        wallMeshes = [];
        const wallMaterial =
          tileTexture &&
          (currentApplication === "wall" || currentApplication === "both")
            ? createTileMaterial(tileTexture, currentPattern)
            : new THREE.MeshStandardMaterial({
                color: 0xe8e8e8,
                roughness: 0.8,
                metalness: 0.1,
                normalMap: createWallNormalMap(),
              });

        // Back wall
        const backWallGeometry = new THREE.PlaneGeometry(15, wallHeight);
        const backWall = new THREE.Mesh(backWallGeometry, wallMaterial);
        backWall.position.z = -7.5;
        backWall.position.y = wallHeight / 2;
        backWall.receiveShadow = true;
        wallMeshes.push(backWall);
        roomGroup.add(backWall);

        // Left wall
        const leftWallGeometry = new THREE.PlaneGeometry(15, wallHeight);
        const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial);
        leftWall.rotation.y = Math.PI / 2;
        leftWall.position.x = -7.5;
        leftWall.position.y = wallHeight / 2;
        leftWall.receiveShadow = true;
        wallMeshes.push(leftWall);
        roomGroup.add(leftWall);

        // Right wall
        const rightWallGeometry = new THREE.PlaneGeometry(15, wallHeight);
        const rightWall = new THREE.Mesh(rightWallGeometry, wallMaterial);
        rightWall.rotation.y = -Math.PI / 2;
        rightWall.position.x = 7.5;
        rightWall.position.y = wallHeight / 2;
        rightWall.receiveShadow = true;
        wallMeshes.push(rightWall);
        roomGroup.add(rightWall);

        // Add architectural details
        addArchitecturalDetails(roomGroup, wallHeight);

        // Add room-specific elements
        switch (roomType) {
          case "kitchen":
            addUltraRealisticKitchen(roomGroup, wallHeight);
            break;
          case "bathroom":
            addUltraRealisticBathroom(roomGroup, wallHeight);
            break;
          case "living":
            addUltraRealisticLivingRoom(roomGroup, wallHeight);
            break;
          case "bedroom":
            addUltraRealisticBedroom(roomGroup, wallHeight);
            break;
        }

        // Add ultra-realistic decorations
        addUltraRealisticDecorations(roomGroup, wallHeight);

        scene.add(roomGroup);
      }

      function createProceduralMarble() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create marble pattern
        const gradient = ctx.createLinearGradient(0, 0, 512, 512);
        gradient.addColorStop(0, "#f8f8f8");
        gradient.addColorStop(0.5, "#e8e8e8");
        gradient.addColorStop(1, "#f0f0f0");

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);

        // Add marble veins
        ctx.strokeStyle = "rgba(200, 200, 200, 0.3)";
        ctx.lineWidth = 2;
        for (let i = 0; i < 20; i++) {
          ctx.beginPath();
          ctx.moveTo(Math.random() * 512, Math.random() * 512);
          ctx.bezierCurveTo(
            Math.random() * 512,
            Math.random() * 512,
            Math.random() * 512,
            Math.random() * 512,
            Math.random() * 512,
            Math.random() * 512
          );
          ctx.stroke();
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        texture.anisotropy = renderer.capabilities.getMaxAnisotropy();

        return texture;
      }

      function createNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create subtle normal map
        const imageData = ctx.createImageData(512, 512);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          const noise = Math.random() * 30 - 15;
          data[i] = 128 + noise; // R
          data[i + 1] = 128 + noise; // G
          data[i + 2] = 255; // B
          data[i + 3] = 255; // A
        }

        ctx.putImageData(imageData, 0, 0);

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);

        return texture;
      }

      function createWallNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create wall texture with subtle patterns
        ctx.fillStyle = "#e8e8e8";
        ctx.fillRect(0, 0, 512, 512);

        // Add subtle texture
        for (let x = 0; x < 512; x += 4) {
          for (let y = 0; y < 512; y += 4) {
            const brightness = 230 + Math.random() * 20;
            ctx.fillStyle = `rgb(${brightness}, ${brightness}, ${brightness})`;
            ctx.fillRect(x, y, 2, 2);
          }
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        return texture;
      }

      function addArchitecturalDetails(group, wallHeight) {
        // Add ultra-realistic baseboards
        const baseboardMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.3,
          metalness: 0.1,
          normalMap: createWoodNormalMap(),
        });

        // Create baseboard profile
        const baseboardShape = new THREE.Shape();
        baseboardShape.moveTo(0, 0);
        baseboardShape.lineTo(0, 0.15);
        baseboardShape.lineTo(0.02, 0.15);
        baseboardShape.lineTo(0.02, 0.12);
        baseboardShape.lineTo(0.05, 0.12);
        baseboardShape.lineTo(0.05, 0.08);
        baseboardShape.lineTo(0.08, 0.08);
        baseboardShape.lineTo(0.08, 0);
        baseboardShape.lineTo(0, 0);

        const baseboardGeometry = new THREE.ExtrudeGeometry(baseboardShape, {
          depth: 15,
          bevelEnabled: false,
        });

        // Back wall baseboard
        const backBaseboard = new THREE.Mesh(
          baseboardGeometry,
          baseboardMaterial
        );
        backBaseboard.position.set(0, 0, -7.5);
        group.add(backBaseboard);

        // Left wall baseboard
        const leftBaseboard = new THREE.Mesh(
          baseboardGeometry,
          baseboardMaterial
        );
        leftBaseboard.rotation.y = Math.PI / 2;
        leftBaseboard.position.set(-7.5, 0, 0);
        group.add(leftBaseboard);

        // Right wall baseboard
        const rightBaseboard = new THREE.Mesh(
          baseboardGeometry,
          baseboardMaterial
        );
        rightBaseboard.rotation.y = Math.PI / 2;
        rightBaseboard.position.set(7.5, 0, 0);
        group.add(rightBaseboard);

        // Add crown molding
        const moldingMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.3,
          metalness: 0.1,
        });

        const moldingShape = new THREE.Shape();
        moldingShape.moveTo(0, 0);
        moldingShape.lineTo(0, 0.1);
        moldingShape.lineTo(0.03, 0.1);
        moldingShape.lineTo(0.03, 0.07);
        moldingShape.lineTo(0.06, 0.07);
        moldingShape.lineTo(0.06, 0.04);
        moldingShape.lineTo(0.08, 0.04);
        moldingShape.lineTo(0.08, 0);
        moldingShape.lineTo(0, 0);

        const moldingGeometry = new THREE.ExtrudeGeometry(moldingShape, {
          depth: 15,
          bevelEnabled: false,
        });

        // Back wall molding
        const backMolding = new THREE.Mesh(moldingGeometry, moldingMaterial);
        backMolding.position.set(0, wallHeight, -7.5);
        group.add(backMolding);

        // Add ultra-realistic windows
        addUltraRealisticWindows(group, wallHeight);
      }

      function createWoodNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create wood grain pattern
        ctx.fillStyle = "#8B4513";
        ctx.fillRect(0, 0, 512, 512);

        // Add wood grain
        ctx.strokeStyle = "rgba(101, 67, 33, 0.5)";
        ctx.lineWidth = 1;
        for (let y = 0; y < 512; y += 2) {
          ctx.beginPath();
          ctx.moveTo(0, y + Math.sin(y * 0.02) * 5);
          for (let x = 0; x < 512; x += 10) {
            ctx.lineTo(x, y + Math.sin((x + y) * 0.02) * 5);
          }
          ctx.stroke();
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        return texture;
      }

      function addUltraRealisticWindows(group, wallHeight) {
        const windowFrameMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.2,
          metalness: 0.8,
          envMapIntensity: 0.5,
        });

        const glassMaterial = new THREE.MeshPhysicalMaterial({
          color: 0x87ceeb,
          metalness: 0,
          roughness: 0,
          transmission: 0.95,
          transparent: true,
          opacity: 0.2,
          envMapIntensity: 0.5,
          clearcoat: 1.0,
          clearcoatRoughness: 0.1,
        });

        // Large window on back wall
        const windowGroup = new THREE.Group();

        // Window frame
        const frameGeometry = new THREE.BoxGeometry(4, 3, 0.3);
        const frame = new THREE.Mesh(frameGeometry, windowFrameMaterial);
        frame.position.set(0, wallHeight * 0.6, -7.5);
        frame.castShadow = true;
        windowGroup.add(frame);

        // Window panes
        const paneGeometry = new THREE.BoxGeometry(1.8, 1.3, 0.1);
        const paneMaterial = glassMaterial.clone();

        // Top left pane
        const pane1 = new THREE.Mesh(paneGeometry, paneMaterial);
        pane1.position.set(-0.95, wallHeight * 0.6, -7.65);
        windowGroup.add(pane1);

        // Top right pane
        const pane2 = new THREE.Mesh(paneGeometry, paneMaterial);
        pane2.position.set(0.95, wallHeight * 0.6, -7.65);
        windowGroup.add(pane2);

        // Bottom left pane
        const pane3 = new THREE.Mesh(paneGeometry, paneMaterial);
        pane3.position.set(-0.95, wallHeight * 0.35, -7.65);
        windowGroup.add(pane3);

        // Bottom right pane
        const pane4 = new THREE.Mesh(paneGeometry, paneMaterial);
        pane4.position.set(0.95, wallHeight * 0.35, -7.65);
        windowGroup.add(pane4);

        // Window dividers
        const dividerGeometry = new THREE.BoxGeometry(0.1, 3, 0.2);
        const divider = new THREE.Mesh(dividerGeometry, windowFrameMaterial);
        divider.position.set(0, wallHeight * 0.6, -7.6);
        windowGroup.add(divider);

        const horizontalDividerGeometry = new THREE.BoxGeometry(4, 0.1, 0.2);
        const horizontalDivider = new THREE.Mesh(
          horizontalDividerGeometry,
          windowFrameMaterial
        );
        horizontalDivider.position.set(0, wallHeight * 0.475, -7.6);
        windowGroup.add(horizontalDivider);

        // Window sill
        const sillGeometry = new THREE.BoxGeometry(4.2, 0.15, 0.5);
        const sillMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.3,
          metalness: 0.1,
          normalMap: createMarbleNormalMap(),
        });
        const sill = new THREE.Mesh(sillGeometry, sillMaterial);
        sill.position.set(0, wallHeight * 0.25, -7.25);
        sill.castShadow = true;
        sill.receiveShadow = true;
        windowGroup.add(sill);

        // Curtains
        const curtainGeometry = new THREE.PlaneGeometry(4.5, wallHeight * 0.7);
        const curtainMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.9,
          metalness: 0.1,
          side: THREE.DoubleSide,
          transparent: true,
          opacity: 0.8,
        });

        const leftCurtain = new THREE.Mesh(curtainGeometry, curtainMaterial);
        leftCurtain.position.set(-2.2, wallHeight * 0.5, -7.4);
        windowGroup.add(leftCurtain);

        const rightCurtain = new THREE.Mesh(curtainGeometry, curtainMaterial);
        rightCurtain.position.set(2.2, wallHeight * 0.5, -7.4);
        windowGroup.add(rightCurtain);

        group.add(windowGroup);
      }

      function createMarbleNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create marble pattern
        const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
        gradient.addColorStop(0, "#f0f0f0");
        gradient.addColorStop(0.5, "#e0e0e0");
        gradient.addColorStop(1, "#f5f5f5");

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);

        // Add marble veins
        for (let i = 0; i < 30; i++) {
          ctx.strokeStyle = `rgba(200, 200, 200, ${Math.random() * 0.3})`;
          ctx.lineWidth = Math.random() * 3 + 1;
          ctx.beginPath();
          ctx.moveTo(Math.random() * 512, Math.random() * 512);

          const cp1x = Math.random() * 512;
          const cp1y = Math.random() * 512;
          const cp2x = Math.random() * 512;
          const cp2y = Math.random() * 512;
          const x = Math.random() * 512;
          const y = Math.random() * 512;

          ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);
          ctx.stroke();
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        return texture;
      }

      function addUltraRealisticKitchen(group, wallHeight) {
        // Central kitchen island with ultra details
        const islandGroup = new THREE.Group();

        // Island base with paneling
        const islandBaseGeometry = new THREE.BoxGeometry(4, 0.95, 2.5);
        const islandMaterial = new THREE.MeshStandardMaterial({
          color: 0x2c2c2c,
          roughness: 0.3,
          metalness: 0.7,
          normalMap: createMetalNormalMap(),
        });
        const islandBase = new THREE.Mesh(islandBaseGeometry, islandMaterial);
        islandBase.position.set(0, 0.475, 0);
        islandBase.castShadow = true;
        islandBase.receiveShadow = true;
        islandGroup.add(islandBase);

        // Countertop with marble
        const counterGeometry = new THREE.BoxGeometry(4.1, 0.05, 2.6);
        const counterMaterial = new THREE.MeshStandardMaterial({
          color: 0xf5f5f5,
          roughness: 0.1,
          metalness: 0.1,
          normalMap: createMarbleNormalMap(),
          normalScale: new THREE.Vector2(1.5, 1.5),
        });
        const counter = new THREE.Mesh(counterGeometry, counterMaterial);
        counter.position.set(0, 0.975, 0);
        counter.castShadow = true;
        counter.receiveShadow = true;
        islandGroup.add(counter);

        // Sink in island
        const sinkGeometry = new THREE.BoxGeometry(1.2, 0.1, 0.8);
        const sinkMaterial = new THREE.MeshStandardMaterial({
          color: 0xe0e0e0,
          roughness: 0.1,
          metalness: 0.9,
        });
        const sink = new THREE.Mesh(sinkGeometry, sinkMaterial);
        sink.position.set(0, 0.92, 0);
        islandGroup.add(sink);

        // Faucet
        const faucetGroup = new THREE.Group();

        // Faucet base
        const faucetBaseGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.1);
        const faucetMaterial = new THREE.MeshStandardMaterial({
          color: 0xc0c0c0,
          roughness: 0.1,
          metalness: 0.9,
          envMapIntensity: 0.8,
        });
        const faucetBase = new THREE.Mesh(faucetBaseGeometry, faucetMaterial);
        faucetBase.position.set(0, 0.98, 0.3);
        faucetGroup.add(faucetBase);

        // Faucet neck
        const neckGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.3);
        const neck = new THREE.Mesh(neckGeometry, faucetMaterial);
        neck.position.set(0, 1.13, 0.3);
        neck.rotation.z = Math.PI / 6;
        faucetGroup.add(neck);

        // Faucet head
        const headGeometry = new THREE.CylinderGeometry(0.025, 0.02, 0.05);
        const head = new THREE.Mesh(headGeometry, faucetMaterial);
        head.position.set(0.13, 1.08, 0.3);
        head.rotation.z = Math.PI / 6;
        faucetGroup.add(head);

        islandGroup.add(faucetGroup);

        // Stove top
        const stoveGeometry = new THREE.BoxGeometry(1, 0.02, 0.8);
        const stoveMaterial = new THREE.MeshStandardMaterial({
          color: 0x1a1a1a,
          roughness: 0.2,
          metalness: 0.8,
          emissive: 0x222222,
          emissiveIntensity: 0.2,
        });
        const stove = new THREE.Mesh(stoveGeometry, stoveMaterial);
        stove.position.set(1.2, 0.98, 0);
        islandGroup.add(stove);

        // Burners
        for (let i = 0; i < 2; i++) {
          const burnerGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.01);
          const burnerMaterial = new THREE.MeshStandardMaterial({
            color: 0x333333,
            roughness: 0.3,
            metalness: 0.7,
            emissive: 0x444444,
            emissiveIntensity: 0.1,
          });
          const burner = new THREE.Mesh(burnerGeometry, burnerMaterial);
          burner.position.set(1.2 + (i - 0.5) * 0.4, 0.99, 0);
          islandGroup.add(burner);
        }

        // Bar stools
        for (let i = 0; i < 3; i++) {
          const stoolGroup = new THREE.Group();

          // Seat with cushion
          const seatGeometry = new THREE.CylinderGeometry(0.25, 0.25, 0.08);
          const seatMaterial = new THREE.MeshStandardMaterial({
            color: 0x4169e1,
            roughness: 0.8,
            metalness: 0.2,
          });
          const seat = new THREE.Mesh(seatGeometry, seatMaterial);
          seat.position.set(0, 1.3, 0);
          seat.castShadow = true;
          stoolGroup.add(seat);

          // Seat cushion
          const cushionGeometry = new THREE.CylinderGeometry(0.24, 0.24, 0.03);
          const cushionMaterial = new THREE.MeshStandardMaterial({
            color: 0x1e90ff,
            roughness: 0.9,
            metalness: 0.1,
          });
          const cushion = new THREE.Mesh(cushionGeometry, cushionMaterial);
          cushion.position.set(0, 1.355, 0);
          stoolGroup.add(cushion);

          // Leg with hydraulic base
          const legGeometry = new THREE.CylinderGeometry(0.04, 0.04, 1.3);
          const legMaterial = new THREE.MeshStandardMaterial({
            color: 0x2c2c2c,
            roughness: 0.3,
            metalness: 0.7,
          });
          const leg = new THREE.Mesh(legGeometry, legMaterial);
          leg.position.set(0, 0.65, 0);
          leg.castShadow = true;
          stoolGroup.add(leg);

          // Footrest
          const footrestGeometry = new THREE.TorusGeometry(0.15, 0.02, 8, 16);
          const footrest = new THREE.Mesh(footrestGeometry, legMaterial);
          footrest.position.set(0, 0.3, 0);
          footrest.rotation.x = Math.PI / 2;
          stoolGroup.add(footrest);

          stoolGroup.position.set((i - 1) * 1.3, 0, 1.5);
          islandGroup.add(stoolGroup);
        }

        // Decorative items on counter
        // Pot
        const potGeometry = new THREE.CylinderGeometry(0.15, 0.12, 0.2);
        const potMaterial = new THREE.MeshStandardMaterial({
          color: 0x2c2c2c,
          roughness: 0.3,
          metalness: 0.7,
        });
        const pot = new THREE.Mesh(potGeometry, potMaterial);
        pot.position.set(-0.8, 1.02, -0.5);
        pot.castShadow = true;
        islandGroup.add(pot);

        // Pot handle
        const handleGeometry = new THREE.TorusGeometry(
          0.08,
          0.01,
          8,
          16,
          Math.PI
        );
        const handle = new THREE.Mesh(handleGeometry, potMaterial);
        handle.position.set(-0.8, 1.02, -0.5);
        handle.rotation.y = Math.PI / 2;
        islandGroup.add(handle);

        // Green apples
        for (let i = 0; i < 2; i++) {
          const appleGeometry = new THREE.SphereGeometry(0.05);
          const appleMaterial = new THREE.MeshStandardMaterial({
            color: 0x228b22,
            roughness: 0.7,
            metalness: 0.1,
          });
          const apple = new THREE.Mesh(appleGeometry, appleMaterial);
          apple.position.set(0.5 + i * 0.1, 1.02, -0.3);
          apple.castShadow = true;
          islandGroup.add(apple);

          // Apple stem
          const stemGeometry = new THREE.CylinderGeometry(0.005, 0.005, 0.02);
          const stemMaterial = new THREE.MeshStandardMaterial({
            color: 0x8b4513,
            roughness: 0.9,
            metalness: 0.1,
          });
          const stem = new THREE.Mesh(stemGeometry, stemMaterial);
          stem.position.set(0.5 + i * 0.1, 1.07, -0.3);
          islandGroup.add(stem);
        }

        group.add(islandGroup);

        // Wall cabinets with glass doors
        const cabinetGroup = new THREE.Group();

        // Left side cabinets - glass display
        for (let i = 0; i < 2; i++) {
          const cabinetGeometry = new THREE.BoxGeometry(1.2, 0.8, 0.4);
          const cabinetMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a1a1a,
            roughness: 0.2,
            metalness: 0.8,
          });
          const cabinet = new THREE.Mesh(cabinetGeometry, cabinetMaterial);
          cabinet.position.set(-4, wallHeight * 0.7, -7.3);
          cabinet.position.y -= i * 0.9;
          cabinet.castShadow = true;
          cabinet.receiveShadow = true;
          cabinetGroup.add(cabinet);

          // Glass door
          const glassGeometry = new THREE.BoxGeometry(1.1, 0.7, 0.05);
          const glassMaterial = new THREE.MeshPhysicalMaterial({
            color: 0x000000,
            metalness: 0,
            roughness: 0,
            transmission: 0.9,
            transparent: true,
            opacity: 0.3,
            envMapIntensity: 0.5,
          });
          const glass = new THREE.Mesh(glassGeometry, glassMaterial);
          glass.position.set(-4, wallHeight * 0.7, -7.1);
          glass.position.y -= i * 0.9;
          cabinetGroup.add(glass);

          // Wine glasses inside
          for (let j = 0; j < 3; j++) {
            const glassGroup = new THREE.Group();

            // Glass bowl
            const bowlGeometry = new THREE.CylinderGeometry(
              0.03,
              0.04,
              0.08,
              16
            );
            const bowlMaterial = new THREE.MeshPhysicalMaterial({
              color: 0xffffff,
              metalness: 0,
              roughness: 0,
              transmission: 0.95,
              transparent: true,
              opacity: 0.3,
            });
            const bowl = new THREE.Mesh(bowlGeometry, bowlMaterial);
            bowl.position.set(0, 0, 0);
            glassGroup.add(bowl);

            // Glass stem
            const stemGeometry = new THREE.CylinderGeometry(0.003, 0.003, 0.08);
            const stemMaterial = new THREE.MeshStandardMaterial({
              color: 0xffffff,
              roughness: 0.1,
              metalness: 0.9,
            });
            const stem = new THREE.Mesh(stemGeometry, stemMaterial);
            stem.position.set(0, -0.08, 0);
            glassGroup.add(stem);

            // Glass base
            const baseGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.01);
            const base = new THREE.Mesh(baseGeometry, stemMaterial);
            base.position.set(0, -0.125, 0);
            glassGroup.add(base);

            glassGroup.position.set(
              -4 + (j - 1) * 0.3,
              wallHeight * 0.65,
              -7.2
            );
            glassGroup.position.y -= i * 0.9;
            cabinetGroup.add(glassGroup);
          }
        }

        // Right side cabinets - open display
        for (let i = 0; i < 2; i++) {
          const cabinetGeometry = new THREE.BoxGeometry(1.2, 0.8, 0.4);
          const cabinetMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a1a1a,
            roughness: 0.2,
            metalness: 0.8,
          });
          const cabinet = new THREE.Mesh(cabinetGeometry, cabinetMaterial);
          cabinet.position.set(4, wallHeight * 0.7, -7.3);
          cabinet.position.y -= i * 0.9;
          cabinet.castShadow = true;
          cabinet.receiveShadow = true;
          cabinetGroup.add(cabinet);

          // Open shelf
          const shelfGeometry = new THREE.BoxGeometry(1.1, 0.02, 0.35);
          const shelfMaterial = new THREE.MeshStandardMaterial({
            color: 0x2c2c2c,
            roughness: 0.3,
            metalness: 0.7,
          });
          const shelf = new THREE.Mesh(shelfGeometry, shelfMaterial);
          shelf.position.set(4, wallHeight * 0.7, -7.15);
          shelf.position.y -= i * 0.9;
          cabinetGroup.add(shelf);

          // Wine bottles
          for (let j = 0; j < 2; j++) {
            const bottleGroup = new THREE.Group();

            // Bottle body
            const bodyGeometry = new THREE.CylinderGeometry(0.025, 0.03, 0.15);
            const bodyMaterial = new THREE.MeshPhysicalMaterial({
              color: 0x228b22,
              metalness: 0,
              roughness: 0,
              transmission: 0.8,
              transparent: true,
              opacity: 0.7,
            });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.set(0, 0, 0);
            bottleGroup.add(body);

            // Bottle neck
            const neckGeometry = new THREE.CylinderGeometry(0.01, 0.015, 0.05);
            const neckMaterial = new THREE.MeshStandardMaterial({
              color: 0x228b22,
              roughness: 0.3,
              metalness: 0.7,
            });
            const neck = new THREE.Mesh(neckGeometry, neckMaterial);
            neck.position.set(0, 0.1, 0);
            bottleGroup.add(neck);

            // Bottle cap
            const capGeometry = new THREE.CylinderGeometry(0.012, 0.012, 0.02);
            const capMaterial = new THREE.MeshStandardMaterial({
              color: 0xffd700,
              roughness: 0.2,
              metalness: 0.8,
            });
            const cap = new THREE.Mesh(capGeometry, capMaterial);
            cap.position.set(0, 0.135, 0);
            bottleGroup.add(cap);

            bottleGroup.position.set(
              4 + (j - 0.5) * 0.4,
              wallHeight * 0.65,
              -7.2
            );
            bottleGroup.position.y -= i * 0.9;
            cabinetGroup.add(bottleGroup);
          }

          // Additional wine glasses
          for (let j = 0; j < 2; j++) {
            const glassGroup = new THREE.Group();

            // Glass bowl
            const bowlGeometry = new THREE.CylinderGeometry(
              0.03,
              0.04,
              0.08,
              16
            );
            const bowlMaterial = new THREE.MeshPhysicalMaterial({
              color: 0xffffff,
              metalness: 0,
              roughness: 0,
              transmission: 0.95,
              transparent: true,
              opacity: 0.3,
            });
            const bowl = new THREE.Mesh(bowlGeometry, bowlMaterial);
            bowl.position.set(0, 0, 0);
            glassGroup.add(bowl);

            // Glass stem
            const stemGeometry = new THREE.CylinderGeometry(0.003, 0.003, 0.08);
            const stemMaterial = new THREE.MeshStandardMaterial({
              color: 0xffffff,
              roughness: 0.1,
              metalness: 0.9,
            });
            const stem = new THREE.Mesh(stemGeometry, stemMaterial);
            stem.position.set(0, -0.08, 0);
            glassGroup.add(stem);

            // Glass base
            const baseGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.01);
            const base = new THREE.Mesh(baseGeometry, stemMaterial);
            base.position.set(0, -0.125, 0);
            glassGroup.add(base);

            glassGroup.position.set(
              4 + (j - 0.5) * 0.4,
              wallHeight * 0.6,
              -7.2
            );
            glassGroup.position.y -= i * 0.9;
            cabinetGroup.add(glassGroup);
          }
        }

        // Bottom cabinets with drawers
        for (let i = 0; i < 3; i++) {
          const drawerGeometry = new THREE.BoxGeometry(1.2, 0.3, 0.4);
          const drawerMaterial = new THREE.MeshStandardMaterial({
            color: 0x8b4513,
            roughness: 0.7,
            metalness: 0.3,
            normalMap: createWoodNormalMap(),
          });
          const drawer = new THREE.Mesh(drawerGeometry, drawerMaterial);
          drawer.position.set((i - 1) * 1.3, 0.35, -7.3);
          drawer.castShadow = true;
          drawer.receiveShadow = true;
          cabinetGroup.add(drawer);

          // Drawer handle
          const handleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.15);
          const handleMaterial = new THREE.MeshStandardMaterial({
            color: 0xc0c0c0,
            roughness: 0.2,
            metalness: 0.8,
          });
          const handle = new THREE.Mesh(handleGeometry, handleMaterial);
          handle.position.set((i - 1) * 1.3, 0.35, -7.1);
          handle.rotation.z = Math.PI / 2;
          cabinetGroup.add(handle);
        }

        group.add(cabinetGroup);

        // Ultra-realistic pendant lights
        for (let i = 0; i < 3; i++) {
          const pendantGroup = new THREE.Group();

          // Cable
          const cableGeometry = new THREE.CylinderGeometry(0.005, 0.005, 1.5);
          const cableMaterial = new THREE.MeshStandardMaterial({
            color: 0x2c2c2c,
            roughness: 0.5,
            metalness: 0.5,
          });
          const cable = new THREE.Mesh(cableGeometry, cableMaterial);
          cable.position.set((i - 1) * 1.3, wallHeight - 0.75, 0);
          pendantGroup.add(cable);

          // Light fixture
          const fixtureGeometry = new THREE.ConeGeometry(0.15, 0.25, 32);
          const fixtureMaterial = new THREE.MeshStandardMaterial({
            color: 0x2c2c2c,
            roughness: 0.3,
            metalness: 0.7,
            side: THREE.DoubleSide,
          });
          const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
          fixture.position.set((i - 1) * 1.3, wallHeight - 1.6, 0);
          pendantGroup.add(fixture);

          // Light bulb
          const bulbGeometry = new THREE.SphereGeometry(0.05);
          const bulbMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            emissive: 0xffd700,
            emissiveIntensity: 0.8,
          });
          const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial);
          bulb.position.set((i - 1) * 1.3, wallHeight - 1.7, 0);
          pendantGroup.add(bulb);

          // Point light for realistic illumination
          const pointLight = new THREE.PointLight(0xffd700, 0.5, 8);
          pointLight.position.set((i - 1) * 1.3, wallHeight - 1.7, 0);
          pointLight.castShadow = true;
          pendantGroup.add(pointLight);

          group.add(pendantGroup);
        }
      }

      function createMetalNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create brushed metal pattern
        ctx.fillStyle = "#808080";
        ctx.fillRect(0, 0, 512, 512);

        // Add brush strokes
        for (let y = 0; y < 512; y += 2) {
          const brightness = 120 + Math.sin(y * 0.1) * 20;
          ctx.strokeStyle = `rgb(${brightness}, ${brightness}, ${brightness})`;
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(0, y);
          ctx.lineTo(512, y);
          ctx.stroke();
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        return texture;
      }

      function addUltraRealisticBathroom(group, wallHeight) {
        // Ultra-luxurious bathtub
        const bathtubGroup = new THREE.Group();

        // Tub body with freestanding design
        const tubGeometry = new THREE.CylinderGeometry(0.8, 0.9, 1.8, 32);
        const tubMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.1,
          metalness: 0.9,
          envMapIntensity: 0.8,
        });
        const tub = new THREE.Mesh(tubGeometry, tubMaterial);
        tub.position.set(4, 0.45, -2);
        tub.rotation.z = Math.PI / 2;
        tub.castShadow = true;
        tub.receiveShadow = true;
        bathtubGroup.add(tub);

        // Tub feet
        for (let i = 0; i < 4; i++) {
          const footGeometry = new THREE.SphereGeometry(0.08);
          const footMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            roughness: 0.2,
            metalness: 0.8,
          });
          const foot = new THREE.Mesh(footGeometry, footMaterial);
          const angle = (Math.PI / 2) * i;
          foot.position.set(
            4 + Math.cos(angle) * 0.7,
            0.08,
            -2 + Math.sin(angle) * 0.7
          );
          bathtubGroup.add(foot);
        }

        // Freestanding faucet
        const faucetGroup = new THREE.Group();

        // Faucet base
        const baseGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.8);
        const faucetMaterial = new THREE.MeshStandardMaterial({
          color: 0xffd700,
          roughness: 0.1,
          metalness: 0.9,
          envMapIntensity: 0.8,
        });
        const base = new THREE.Mesh(baseGeometry, faucetMaterial);
        base.position.set(4.8, 0.4, -2);
        faucetGroup.add(base);

        // Faucet spout
        const spoutGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.4);
        const spout = new THREE.Mesh(spoutGeometry, faucetMaterial);
        spout.position.set(4.8, 0.9, -2);
        spout.rotation.z = -Math.PI / 4;
        faucetGroup.add(spout);

        // Faucet handles
        for (let i = 0; i < 2; i++) {
          const handleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.15);
          const handle = new THREE.Mesh(handleGeometry, faucetMaterial);
          handle.position.set(4.8 + (i - 0.5) * 0.1, 0.85, -2);
          handle.rotation.x = Math.PI / 2;
          faucetGroup.add(handle);
        }

        bathtubGroup.add(faucetGroup);

        group.add(bathtubGroup);

        // Luxury walk-in shower
        const showerGroup = new THREE.Group();

        // Shower base
        const baseGeometry = new THREE.BoxGeometry(2, 0.1, 2);
        const baseMaterial = new THREE.MeshStandardMaterial({
          color: 0xf0f0f0,
          roughness: 0.8,
          metalness: 0.2,
        });
        const showerBase = new THREE.Mesh(baseGeometry, baseMaterial);
        showerBase.position.set(-4, 0.05, -2);
        showerGroup.add(showerBase);

        // Glass walls
        const glassMaterial = new THREE.MeshPhysicalMaterial({
          color: 0xffffff,
          metalness: 0,
          roughness: 0,
          transmission: 0.95,
          transparent: true,
          opacity: 0.1,
        });

        // Back glass wall
        const backGlassGeometry = new THREE.BoxGeometry(2, 2.5, 0.02);
        const backGlass = new THREE.Mesh(backGlassGeometry, glassMaterial);
        backGlass.position.set(-4, 1.25, -3);
        showerGroup.add(backGlass);

        // Side glass wall
        const sideGlassGeometry = new THREE.BoxGeometry(0.02, 2.5, 2);
        const sideGlass = new THREE.Mesh(sideGlassGeometry, glassMaterial);
        sideGlass.position.set(-5, 1.25, -2);
        showerGroup.add(sideGlass);

        // Rain shower head
        const showerHeadGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.1);
        const showerHeadMaterial = new THREE.MeshStandardMaterial({
          color: 0xffd700,
          roughness: 0.1,
          metalness: 0.9,
        });
        const showerHead = new THREE.Mesh(
          showerHeadGeometry,
          showerHeadMaterial
        );
        showerHead.position.set(-4, 2.4, -2);
        showerGroup.add(showerHead);

        // Water effect (particles would be ideal, but we'll use a simple mesh)
        const waterGeometry = new THREE.CylinderGeometry(0.12, 0.12, 0.5);
        const waterMaterial = new THREE.MeshStandardMaterial({
          color: 0x87ceeb,
          transparent: true,
          opacity: 0.3,
        });
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.set(-4, 2.1, -2);
        showerGroup.add(water);

        group.add(showerGroup);

        // Double vanity with marble top
        const vanityGroup = new THREE.Group();

        // Vanity base
        const vanityBaseGeometry = new THREE.BoxGeometry(3, 0.9, 0.6);
        const vanityMaterial = new THREE.MeshStandardMaterial({
          color: 0x2c2c2c,
          roughness: 0.3,
          metalness: 0.7,
        });
        const vanityBase = new THREE.Mesh(vanityBaseGeometry, vanityMaterial);
        vanityBase.position.set(0, 0.45, 4);
        vanityBase.castShadow = true;
        vanityBase.receiveShadow = true;
        vanityGroup.add(vanityBase);

        // Marble countertop
        const counterGeometry = new THREE.BoxGeometry(3.1, 0.05, 0.65);
        const counterMaterial = new THREE.MeshStandardMaterial({
          color: 0xf5f5f5,
          roughness: 0.1,
          metalness: 0.1,
          normalMap: createMarbleNormalMap(),
        });
        const counter = new THREE.Mesh(counterGeometry, counterMaterial);
        counter.position.set(0, 0.925, 4);
        counter.castShadow = true;
        vanityGroup.add(counter);

        // Double sinks
        for (let i = 0; i < 2; i++) {
          const sinkGeometry = new THREE.CylinderGeometry(0.25, 0.2, 0.15);
          const sinkMaterial = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.1,
            metalness: 0.9,
          });
          const sink = new THREE.Mesh(sinkGeometry, sinkMaterial);
          sink.position.set((i - 0.5) * 1.2, 0.85, 4);
          vanityGroup.add(sink);

          // Faucet
          const faucetGroup = new THREE.Group();

          const faucetBaseGeometry = new THREE.CylinderGeometry(
            0.02,
            0.02,
            0.1
          );
          const faucetMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            roughness: 0.1,
            metalness: 0.9,
          });
          const faucetBase = new THREE.Mesh(faucetBaseGeometry, faucetMaterial);
          faucetBase.position.set((i - 0.5) * 1.2, 0.95, 4);
          faucetGroup.add(faucetBase);

          const faucetSpoutGeometry = new THREE.CylinderGeometry(
            0.015,
            0.015,
            0.2
          );
          const spout = new THREE.Mesh(faucetSpoutGeometry, faucetMaterial);
          spout.position.set((i - 0.5) * 1.2, 1.05, 4);
          spout.rotation.z = Math.PI / 6;
          faucetGroup.add(spout);

          vanityGroup.add(faucetGroup);
        }

        // Large mirror
        const mirrorGeometry = new THREE.PlaneGeometry(2.8, 1.2);
        const mirrorMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0,
          metalness: 1,
          envMapIntensity: 0.9,
        });
        const mirror = new THREE.Mesh(mirrorGeometry, mirrorMaterial);
        mirror.position.set(0, 1.8, 4.35);
        vanityGroup.add(mirror);

        // Mirror lights
        for (let i = 0; i < 2; i++) {
          const lightGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.3);
          const lightMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            emissive: 0xffd700,
            emissiveIntensity: 0.5,
          });
          const light = new THREE.Mesh(lightGeometry, lightMaterial);
          light.position.set((i - 0.5) * 1.6, 1.8, 4.3);
          vanityGroup.add(light);
        }

        group.add(vanityGroup);
      }

      function addUltraRealisticLivingRoom(group, wallHeight) {
        // Ultra-comfortable sectional sofa
        const sofaGroup = new THREE.Group();

        // Main sofa section
        const sofaGeometry = new THREE.BoxGeometry(5, 0.8, 2.5);
        const sofaMaterial = new THREE.MeshStandardMaterial({
          color: 0x4682b4,
          roughness: 0.9,
          metalness: 0.1,
        });
        const sofa = new THREE.Mesh(sofaGeometry, sofaMaterial);
        sofa.position.set(0, 0.4, -1);
        sofa.castShadow = true;
        sofa.receiveShadow = true;
        sofaGroup.add(sofa);

        // Sofa back cushions
        for (let i = 0; i < 4; i++) {
          const cushionGeometry = new THREE.BoxGeometry(1.1, 0.6, 0.4);
          const cushion = new THREE.Mesh(cushionGeometry, sofaMaterial);
          cushion.position.set(-1.5 + i * 1, 0.7, -0.2);
          cushion.castShadow = true;
          sofaGroup.add(cushion);
        }

        // Seat cushions
        for (let i = 0; i < 3; i++) {
          const seatGeometry = new THREE.BoxGeometry(1.4, 0.15, 0.8);
          const seatMaterial = new THREE.MeshStandardMaterial({
            color: 0x5a9fd4,
            roughness: 0.8,
            metalness: 0.2,
          });
          const seat = new THREE.Mesh(seatGeometry, seatMaterial);
          seat.position.set(-1.4 + i * 1.4, 0.475, -0.5);
          seat.castShadow = true;
          sofaGroup.add(seat);
        }

        // Chaise lounge section
        const chaiseGeometry = new THREE.BoxGeometry(2.5, 0.8, 1.5);
        const chaise = new THREE.Mesh(chaiseGeometry, sofaMaterial);
        chaise.position.set(2.5, 0.4, 0.25);
        chaise.castShadow = true;
        chaise.receiveShadow = true;
        sofaGroup.add(chaise);

        // Ottoman
        const ottomanGeometry = new THREE.BoxGeometry(1.5, 0.4, 1);
        const ottoman = new THREE.Mesh(ottomanGeometry, sofaMaterial);
        ottoman.position.set(-2, 0.2, 1.5);
        ottoman.castShadow = true;
        ottoman.receiveShadow = true;
        sofaGroup.add(ottoman);

        group.add(sofaGroup);

        // Entertainment center
        const entertainmentGroup = new THREE.Group();

        // TV console
        const consoleGeometry = new THREE.BoxGeometry(4, 0.5, 0.5);
        const consoleMaterial = new THREE.MeshStandardMaterial({
          color: 0x2c2c2c,
          roughness: 0.3,
          metalness: 0.7,
        });
        const consoleBody = new THREE.Mesh(consoleGeometry, consoleMaterial);
        consoleBody.position.set(0, 0.25, -5.5);
        consoleBody.castShadow = true;
        consoleBody.receiveShadow = true;
        entertainmentGroup.add(consoleBody);

        // Large TV
        const tvGeometry = new THREE.BoxGeometry(3.5, 2, 0.1);
        const tvMaterial = new THREE.MeshStandardMaterial({
          color: 0x111111,
          roughness: 0.2,
          metalness: 0.8,
        });
        const tv = new THREE.Mesh(tvGeometry, tvMaterial);
        tv.position.set(0, 1.5, -5.7);
        tv.castShadow = true;
        entertainmentGroup.add(tv);

        // TV screen
        const screenGeometry = new THREE.PlaneGeometry(3.3, 1.8);
        const screenMaterial = new THREE.MeshBasicMaterial({
          color: 0x000080,
          emissive: 0x000080,
          emissiveIntensity: 0.3,
        });
        const screen = new THREE.Mesh(screenGeometry, screenMaterial);
        screen.position.set(0, 1.5, -5.65);
        entertainmentGroup.add(screen);

        // Sound bar
        const soundbarGeometry = new THREE.BoxGeometry(1, 0.1, 0.2);
        const soundbarMaterial = new THREE.MeshStandardMaterial({
          color: 0x1a1a1a,
          roughness: 0.3,
          metalness: 0.7,
        });
        const soundbar = new THREE.Mesh(soundbarGeometry, soundbarMaterial);
        soundbar.position.set(0, 0.55, -5.4);
        entertainmentGroup.add(soundbar);

        group.add(entertainmentGroup);

        // Coffee table with glass top
        const tableGroup = new THREE.Group();

        // Table base
        const baseGeometry = new THREE.BoxGeometry(1.2, 0.4, 0.8);
        const baseMaterial = new THREE.MeshStandardMaterial({
          color: 0x8b4513,
          roughness: 0.7,
          metalness: 0.3,
          normalMap: createWoodNormalMap(),
        });
        const base = new THREE.Mesh(baseGeometry, baseMaterial);
        base.position.set(0, 0.2, 1);
        base.castShadow = true;
        base.receiveShadow = true;
        tableGroup.add(base);

        // Glass top
        const topGeometry = new THREE.BoxGeometry(1.4, 0.05, 1);
        const topMaterial = new THREE.MeshPhysicalMaterial({
          color: 0xffffff,
          metalness: 0,
          roughness: 0,
          transmission: 0.9,
          transparent: true,
          opacity: 0.3,
        });
        const top = new THREE.Mesh(topGeometry, topMaterial);
        top.position.set(0, 0.425, 1);
        tableGroup.add(top);

        group.add(tableGroup);

        // Area rug
        const rugGeometry = new THREE.PlaneGeometry(6, 5);
        const rugMaterial = new THREE.MeshStandardMaterial({
          color: 0x8b4513,
          roughness: 0.9,
          metalness: 0.1,
          normalMap: createRugNormalMap(),
        });
        const rug = new THREE.Mesh(rugGeometry, rugMaterial);
        rug.rotation.x = -Math.PI / 2;
        rug.position.set(0, 0.01, 0);
        rug.receiveShadow = true;
        group.add(rug);
      }

      function createRugNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create rug pattern
        ctx.fillStyle = "#8B4513";
        ctx.fillRect(0, 0, 512, 512);

        // Add pattern
        for (let x = 0; x < 512; x += 32) {
          for (let y = 0; y < 512; y += 32) {
            const brightness = 100 + Math.random() * 50;
            ctx.fillStyle = `rgb(${brightness}, ${brightness * 0.7}, ${
              brightness * 0.3
            })`;
            ctx.fillRect(x, y, 30, 30);
          }
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        return texture;
      }

      function addUltraRealisticBedroom(group, wallHeight) {
        // Luxury king-size bed
        const bedGroup = new THREE.Group();

        // Bed frame with upholstered headboard
        const frameGeometry = new THREE.BoxGeometry(2.5, 0.4, 3.5);
        const frameMaterial = new THREE.MeshStandardMaterial({
          color: 0x654321,
          roughness: 0.7,
          metalness: 0.3,
          normalMap: createWoodNormalMap(),
        });
        const frame = new THREE.Mesh(frameGeometry, frameMaterial);
        frame.position.set(0, 0.2, 0);
        frame.castShadow = true;
        frame.receiveShadow = true;
        bedGroup.add(frame);

        // Upholstered headboard
        const headboardGeometry = new THREE.BoxGeometry(2.5, 1.5, 0.2);
        const headboardMaterial = new THREE.MeshStandardMaterial({
          color: 0x4682b4,
          roughness: 0.8,
          metalness: 0.2,
        });
        const headboard = new THREE.Mesh(headboardGeometry, headboardMaterial);
        headboard.position.set(0, 1.1, -1.6);
        headboard.castShadow = true;
        bedGroup.add(headboard);

        // Mattress
        const mattressGeometry = new THREE.BoxGeometry(2.4, 0.4, 3.4);
        const mattressMaterial = new THREE.MeshStandardMaterial({
          color: 0xf5f5f5,
          roughness: 0.9,
          metalness: 0.1,
        });
        const mattress = new THREE.Mesh(mattressGeometry, mattressMaterial);
        mattress.position.set(0, 0.6, 0);
        mattress.castShadow = true;
        mattress.receiveShadow = true;
        bedGroup.add(mattress);

        // Pillows
        for (let i = 0; i < 4; i++) {
          const pillowGeometry = new THREE.BoxGeometry(0.6, 0.2, 0.4);
          const pillowMaterial = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.9,
            metalness: 0.1,
          });
          const pillow = new THREE.Mesh(pillowGeometry, pillowMaterial);
          pillow.position.set(
            (i % 2) * 0.8 - 0.4,
            0.85,
            -1 + Math.floor(i / 2) * 0.5
          );
          pillow.castShadow = true;
          bedGroup.add(pillow);
        }

        // Duvet
        const duvetGeometry = new THREE.BoxGeometry(2.35, 0.1, 2.8);
        const duvetMaterial = new THREE.MeshStandardMaterial({
          color: 0x4682b4,
          roughness: 0.8,
          metalness: 0.2,
        });
        const duvet = new THREE.Mesh(duvetGeometry, duvetMaterial);
        duvet.position.set(0, 0.75, 0.5);
        duvet.castShadow = true;
        bedGroup.add(duvet);

        // Bed skirt
        const skirtGeometry = new THREE.BoxGeometry(2.45, 0.3, 3.45);
        const skirtMaterial = new THREE.MeshStandardMaterial({
          color: 0xf5f5f5,
          roughness: 0.9,
          metalness: 0.1,
        });
        const skirt = new THREE.Mesh(skirtGeometry, skirtMaterial);
        skirt.position.set(0, 0.15, 0);
        bedGroup.add(skirt);

        group.add(bedGroup);

        // Nightstands
        for (let i = 0; i < 2; i++) {
          const nightstandGroup = new THREE.Group();

          // Nightstand body
          const nightstandGeometry = new THREE.BoxGeometry(0.6, 0.6, 0.6);
          const nightstandMaterial = new THREE.MeshStandardMaterial({
            color: 0x654321,
            roughness: 0.7,
            metalness: 0.3,
            normalMap: createWoodNormalMap(),
          });
          const nightstand = new THREE.Mesh(
            nightstandGeometry,
            nightstandMaterial
          );
          nightstand.position.set((i - 0.5) * 3.5, 0.3, -1);
          nightstand.castShadow = true;
          nightstand.receiveShadow = true;
          nightstandGroup.add(nightstand);

          // Table lamp
          const lampGroup = new THREE.Group();

          // Lamp base
          const baseGeometry = new THREE.CylinderGeometry(0.08, 0.1, 0.05);
          const baseMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            roughness: 0.2,
            metalness: 0.8,
          });
          const base = new THREE.Mesh(baseGeometry, baseMaterial);
          base.position.set((i - 0.5) * 3.5, 0.65, -1);
          lampGroup.add(base);

          // Lamp pole
          const poleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.4);
          const poleMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            roughness: 0.2,
            metalness: 0.8,
          });
          const pole = new THREE.Mesh(poleGeometry, poleMaterial);
          pole.position.set((i - 0.5) * 3.5, 0.85, -1);
          lampGroup.add(pole);

          // Lamp shade
          const shadeGeometry = new THREE.ConeGeometry(0.15, 0.2, 32);
          const shadeMaterial = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.9,
            metalness: 0.1,
            side: THREE.DoubleSide,
          });
          const shade = new THREE.Mesh(shadeGeometry, shadeMaterial);
          shade.position.set((i - 0.5) * 3.5, 1.1, -1);
          lampGroup.add(shade);

          // Light bulb
          const bulbGeometry = new THREE.SphereGeometry(0.03);
          const bulbMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700,
            emissive: 0xffd700,
            emissiveIntensity: 0.5,
          });
          const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial);
          bulb.position.set((i - 0.5) * 3.5, 1.05, -1);
          lampGroup.add(bulb);

          nightstandGroup.add(lampGroup);
          group.add(nightstandGroup);
        }

        // Dresser with mirror
        const dresserGroup = new THREE.Group();

        // Dresser body
        const dresserGeometry = new THREE.BoxGeometry(3, 1.2, 0.6);
        const dresserMaterial = new THREE.MeshStandardMaterial({
          color: 0x654321,
          roughness: 0.7,
          metalness: 0.3,
          normalMap: createWoodNormalMap(),
        });
        const dresser = new THREE.Mesh(dresserGeometry, dresserMaterial);
        dresser.position.set(0, 0.6, 4);
        dresser.castShadow = true;
        dresser.receiveShadow = true;
        dresserGroup.add(dresser);

        // Dresser top
        const topGeometry = new THREE.BoxGeometry(3.1, 0.05, 0.65);
        const topMaterial = new THREE.MeshStandardMaterial({
          color: 0x8b4513,
          roughness: 0.6,
          metalness: 0.4,
        });
        const top = new THREE.Mesh(topGeometry, topMaterial);
        top.position.set(0, 1.225, 4);
        top.castShadow = true;
        dresserGroup.add(top);

        // Mirror
        const mirrorGeometry = new THREE.PlaneGeometry(2.5, 1.5);
        const mirrorMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0,
          metalness: 1,
          envMapIntensity: 0.9,
        });
        const mirror = new THREE.Mesh(mirrorGeometry, mirrorMaterial);
        mirror.position.set(0, 2, 4.35);
        dresserGroup.add(mirror);

        group.add(dresserGroup);

        // Area rug
        const rugGeometry = new THREE.PlaneGeometry(5, 6);
        const rugMaterial = new THREE.MeshStandardMaterial({
          color: 0x8b4513,
          roughness: 0.9,
          metalness: 0.1,
          normalMap: createRugNormalMap(),
        });
        const rug = new THREE.Mesh(rugGeometry, rugMaterial);
        rug.rotation.x = -Math.PI / 2;
        rug.position.set(0, 0.01, 0);
        rug.receiveShadow = true;
        group.add(rug);

        // Curtains
        for (let i = 0; i < 2; i++) {
          const curtainGeometry = new THREE.PlaneGeometry(2, wallHeight * 0.9);
          const curtainMaterial = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.9,
            metalness: 0.1,
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 0.8,
          });
          const curtain = new THREE.Mesh(curtainGeometry, curtainMaterial);
          curtain.position.set((i - 0.5) * 1.5, wallHeight * 0.45, -7.4);
          group.add(curtain);
        }
      }

      function addUltraRealisticDecorations(group, wallHeight) {
        // Ultra-realistic plants
        const plantPositions = [
          { x: 6, z: 6 },
          { x: -6, z: 6 },
          { x: 6, z: -6 },
          { x: -6, z: -6 },
        ];

        plantPositions.forEach((pos) => {
          const plantGroup = new THREE.Group();

          // Decorative pot
          const potGeometry = new THREE.CylinderGeometry(0.25, 0.2, 0.4);
          const potMaterial = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.3,
            metalness: 0.1,
            normalMap: createCeramicNormalMap(),
          });
          const pot = new THREE.Mesh(potGeometry, potMaterial);
          pot.position.set(pos.x, 0.2, pos.z);
          pot.castShadow = true;
          plantGroup.add(pot);

          // Plant with multiple leaves
          for (let i = 0; i < 5; i++) {
            const leafGeometry = new THREE.SphereGeometry(
              0.2 + Math.random() * 0.1,
              8,
              6
            );
            const leafMaterial = new THREE.MeshStandardMaterial({
              color: new THREE.Color().setHSL(
                0.3 + Math.random() * 0.1,
                0.7,
                0.4
              ),
              roughness: 0.8,
              metalness: 0.1,
            });
            const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
            leaf.position.set(
              pos.x + (Math.random() - 0.5) * 0.3,
              0.5 + Math.random() * 0.3,
              pos.z + (Math.random() - 0.5) * 0.3
            );
            leaf.scale.set(1, 1.5, 0.8);
            leaf.castShadow = true;
            plantGroup.add(leaf);
          }

          group.add(plantGroup);
        });

        // Wall art with frames
        const artPositions = [
          { x: 0, z: -7.4, y: wallHeight * 0.6, rotation: 0 },
          { x: -7.4, z: 0, y: wallHeight * 0.6, rotation: Math.PI / 2 },
          { x: 7.4, z: 0, y: wallHeight * 0.6, rotation: -Math.PI / 2 },
        ];

        artPositions.forEach((pos) => {
          const artGroup = new THREE.Group();

          // Frame
          const frameGeometry = new THREE.BoxGeometry(1.2, 0.9, 0.05);
          const frameMaterial = new THREE.MeshStandardMaterial({
            color: 0x8b4513,
            roughness: 0.6,
            metalness: 0.4,
          });
          const frame = new THREE.Mesh(frameGeometry, frameMaterial);
          frame.position.set(pos.x, pos.y, pos.z);
          if (pos.rotation !== 0) {
            frame.rotation.y = pos.rotation;
          }
          artGroup.add(frame);

          // Artwork
          const artGeometry = new THREE.PlaneGeometry(1, 0.7);
          const artMaterial = new THREE.MeshStandardMaterial({
            color: new THREE.Color().setHSL(Math.random(), 0.5, 0.5),
            roughness: 0.8,
            metalness: 0.2,
          });
          const art = new THREE.Mesh(artGeometry, artMaterial);
          art.position.set(pos.x, pos.y, pos.z + 0.03);
          if (pos.rotation !== 0) {
            art.rotation.y = pos.rotation;
          }
          artGroup.add(art);

          group.add(artGroup);
        });

        // Ceiling fan with light
        const fanGroup = new THREE.Group();

        // Fan base
        const baseGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.1);
        const baseMaterial = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.3,
          metalness: 0.7,
        });
        const base = new THREE.Mesh(baseGeometry, baseMaterial);
        base.position.set(0, wallHeight - 0.05, 0);
        fanGroup.add(base);

        // Fan blades (animated)
        for (let i = 0; i < 4; i++) {
          const bladeGeometry = new THREE.BoxGeometry(2, 0.02, 0.15);
          const bladeMaterial = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.5,
            metalness: 0.5,
          });
          const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
          blade.position.set(0, wallHeight - 0.15, 0);
          blade.rotation.y = (Math.PI / 2) * i;
          blade.userData = {
            isFanBlade: true,
            baseRotation: (Math.PI / 2) * i,
          };
          fanGroup.add(blade);
        }

        // Light fixture
        const lightGeometry = new THREE.SphereGeometry(0.1);
        const lightMaterial = new THREE.MeshStandardMaterial({
          color: 0xffd700,
          emissive: 0xffd700,
          emissiveIntensity: 0.5,
        });
        const light = new THREE.Mesh(lightGeometry, lightMaterial);
        light.position.set(0, wallHeight - 0.25, 0);
        fanGroup.add(light);

        // Point light
        const pointLight = new THREE.PointLight(0xffd700, 0.6, 15);
        pointLight.position.set(0, wallHeight - 0.25, 0);
        pointLight.castShadow = true;
        fanGroup.add(pointLight);

        group.add(fanGroup);
      }

      function createCeramicNormalMap() {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");

        // Create ceramic pattern
        ctx.fillStyle = "#ffffff";
        ctx.fillRect(0, 0, 512, 512);

        // Add subtle ceramic texture
        for (let x = 0; x < 512; x += 8) {
          for (let y = 0; y < 512; y += 8) {
            const brightness = 240 + Math.random() * 15;
            ctx.fillStyle = `rgb(${brightness}, ${brightness}, ${brightness})`;
            ctx.fillRect(x, y, 6, 6);
          }
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        return texture;
      }

      function createTileMaterial(texture, pattern) {
        const tileSize = parseFloat(tileSizeSlider.value);
        const groutWidth = parseFloat(groutWidthSlider.value);
        const groutColor = new THREE.Color(groutColorInput.value);

        // Create canvas for tile pattern
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");

        // Set canvas size based on pattern
        let canvasWidth, canvasHeight;
        switch (pattern) {
          case "grid":
            canvasWidth = canvasHeight = 1024;
            break;
          case "diagonal":
            canvasWidth = canvasHeight = 1024;
            break;
          case "herringbone":
            canvasWidth = 2048;
            canvasHeight = 1024;
            break;
          case "basketweave":
            canvasWidth = 2048;
            canvasHeight = 2048;
            break;
          case "hexagon":
            canvasWidth = 2048;
            canvasHeight = 1772;
            break;
          case "brick":
            canvasWidth = 2048;
            canvasHeight = 1024;
            break;
          default:
            canvasWidth = canvasHeight = 1024;
        }

        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        // Fill with grout color
        context.fillStyle = groutColor.getStyle();
        context.fillRect(0, 0, canvas.width, canvas.height);

        // Create image element from texture
        const img = new Image();
        img.src = texture.image.src;

        // Wait for image to load
        img.onload = function () {
          // Draw pattern based on selected type
          switch (pattern) {
            case "grid":
              drawGridPattern(context, img, tileSize, groutWidth);
              break;
            case "diagonal":
              drawDiagonalPattern(context, img, tileSize, groutWidth);
              break;
            case "herringbone":
              drawHerringbonePattern(context, img, tileSize, groutWidth);
              break;
            case "basketweave":
              drawBasketweavePattern(context, img, tileSize, groutWidth);
              break;
            case "hexagon":
              drawHexagonPattern(context, img, tileSize, groutWidth);
              break;
            case "brick":
              drawBrickPattern(context, img, tileSize, groutWidth);
              break;
          }

          // Update texture
          texture.needsUpdate = true;
        };

        // Create texture from canvas
        const tileTexture = new THREE.CanvasTexture(canvas);
        tileTexture.wrapS = THREE.RepeatWrapping;
        tileTexture.wrapT = THREE.RepeatWrapping;
        tileTexture.repeat.set(4, 4);
        tileTexture.anisotropy = renderer.capabilities.getMaxAnisotropy();

        // Create normal map for tiles
        const normalCanvas = document.createElement("canvas");
        normalCanvas.width = 512;
        normalCanvas.height = 512;
        const normalCtx = normalCanvas.getContext("2d");

        // Create subtle normal map
        const imageData = normalCtx.createImageData(512, 512);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          const noise = Math.random() * 40 - 20;
          data[i] = 128 + noise; // R
          data[i + 1] = 128 + noise; // G
          data[i + 2] = 255; // B
          data[i + 3] = 255; // A
        }

        normalCtx.putImageData(imageData, 0, 0);

        const normalTexture = new THREE.CanvasTexture(normalCanvas);
        normalTexture.wrapS = THREE.RepeatWrapping;
        normalTexture.wrapT = THREE.RepeatWrapping;
        normalTexture.repeat.set(4, 4);

        // Create material with texture
        const material = new THREE.MeshStandardMaterial({
          map: tileTexture,
          normalMap: normalTexture,
          roughness: 0.7,
          metalness: 0.1,
          normalScale: new THREE.Vector2(0.5, 0.5),
        });

        return material;
      }

      function drawGridPattern(context, img, tileSize, groutWidth) {
        const tilePixelSize = 256 * tileSize;
        const groutPixelWidth = groutWidth * 1024;

        for (let y = 0; y < 1024; y += tilePixelSize + groutPixelWidth) {
          for (let x = 0; x < 1024; x += tilePixelSize + groutPixelWidth) {
            context.drawImage(img, x, y, tilePixelSize, tilePixelSize);
          }
        }
      }

      function drawDiagonalPattern(context, img, tileSize, groutWidth) {
        const tilePixelSize = 256 * tileSize;
        const groutPixelWidth = groutWidth * 1024;
        const diagonalSize = tilePixelSize * Math.sqrt(2);

        context.save();
        context.translate(512, 512);
        context.rotate(Math.PI / 4);

        for (let y = -1024; y < 1024; y += diagonalSize + groutPixelWidth) {
          for (let x = -1024; x < 1024; x += diagonalSize + groutPixelWidth) {
            context.drawImage(img, x, y, diagonalSize, diagonalSize);
          }
        }

        context.restore();
      }

      function drawHerringbonePattern(context, img, tileSize, groutWidth) {
        const tileWidth = 256 * tileSize;
        const tileHeight = 128 * tileSize;
        const groutPixelWidth = groutWidth * 1024;

        for (let y = 0; y < 1024; y += tileHeight + groutPixelWidth) {
          for (let x = 0; x < 2048; x += tileWidth + groutPixelWidth) {
            const offset =
              (Math.floor(y / (tileHeight + groutPixelWidth)) % 2) *
              (tileWidth / 2 + groutPixelWidth / 2);

            context.save();
            context.translate(x + offset, y);
            context.rotate(Math.PI / 4);
            context.drawImage(
              img,
              -tileWidth / 2,
              -tileHeight / 2,
              tileWidth,
              tileHeight
            );
            context.restore();
          }
        }
      }

      function drawBasketweavePattern(context, img, tileSize, groutWidth) {
        const tileWidth = 256 * tileSize;
        const tileHeight = 128 * tileSize;
        const groutPixelWidth = groutWidth * 1024;

        for (let y = 0; y < 2048; y += tileHeight * 2 + groutPixelWidth * 2) {
          for (let x = 0; x < 2048; x += tileWidth * 2 + groutPixelWidth * 2) {
            // Horizontal tiles
            context.drawImage(img, x, y, tileWidth * 2, tileHeight);
            context.drawImage(
              img,
              x,
              y + tileHeight + groutPixelWidth,
              tileWidth * 2,
              tileHeight
            );

            // Vertical tiles
            context.save();
            context.translate(
              x + tileWidth + groutPixelWidth,
              y + tileHeight + groutPixelWidth
            );
            context.rotate(Math.PI / 2);
            context.drawImage(
              img,
              -tileHeight,
              -tileWidth,
              tileHeight,
              tileWidth * 2
            );
            context.restore();
          }
        }
      }

      function drawHexagonPattern(context, img, tileSize, groutWidth) {
        const hexRadius = 128 * tileSize;
        const hexHeight = hexRadius * Math.sqrt(3);
        const groutPixelWidth = groutWidth * 1024;

        for (let y = 0; y < 1772; y += hexHeight + groutPixelWidth) {
          for (let x = 0; x < 2048; x += hexRadius * 1.5 + groutPixelWidth) {
            const offset =
              (Math.floor(y / (hexHeight + groutPixelWidth)) % 2) *
              (hexRadius * 0.75 + groutPixelWidth / 2);
            drawHexagon(context, img, x + offset, y, hexRadius);
          }
        }
      }

      function drawHexagon(context, img, centerX, centerY, radius) {
        context.beginPath();
        for (let i = 0; i < 6; i++) {
          const angle = (Math.PI / 3) * i;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          if (i === 0) {
            context.moveTo(x, y);
          } else {
            context.lineTo(x, y);
          }
        }
        context.closePath();
        context.clip();

        context.drawImage(
          img,
          centerX - radius,
          centerY - radius,
          radius * 2,
          radius * 2
        );
        context.restore();
      }

      function drawBrickPattern(context, img, tileSize, groutWidth) {
        const brickWidth = 256 * tileSize;
        const brickHeight = 128 * tileSize;
        const groutPixelWidth = groutWidth * 1024;

        for (let y = 0; y < 1024; y += brickHeight + groutPixelWidth) {
          for (let x = 0; x < 2048; x += brickWidth + groutPixelWidth) {
            const offset =
              (Math.floor(y / (brickHeight + groutPixelWidth)) % 2) *
              (brickWidth / 2 + groutPixelWidth / 2);
            context.drawImage(img, x + offset, y, brickWidth, brickHeight);
          }
        }
      }

      function onWindowResize() {
        const container = document.getElementById("canvas-container");
        camera.aspect = container.clientWidth / container.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(container.clientWidth, container.clientHeight);
      }

      function animate() {
        requestAnimationFrame(animate);

        const delta = clock.getDelta();

        // Animate fan blades
        if (roomGroup) {
          roomGroup.traverse((child) => {
            if (child.userData.isFanBlade) {
              child.rotation.y += delta * 3;
            }
          });
        }

        controls.update();
        renderer.render(scene, camera);
      }

      // Event listeners
      uploadArea.addEventListener("click", () => fileInput.click());

      fileInput.addEventListener("change", (e) => {
        const file = e.target.files[0];
        if (file && file.type.startsWith("image/")) {
          handleImageUpload(file);
        }
      });

      // Drag and drop
      uploadArea.addEventListener("dragover", (e) => {
        e.preventDefault();
        uploadArea.classList.add("dragover");
      });

      uploadArea.addEventListener("dragleave", () => {
        uploadArea.classList.remove("dragover");
      });

      uploadArea.addEventListener("drop", (e) => {
        e.preventDefault();
        uploadArea.classList.remove("dragover");
        const file = e.dataTransfer.files[0];
        if (file && file.type.startsWith("image/")) {
          handleImageUpload(file);
        }
      });

      function handleImageUpload(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new Image();
          img.onload = () => {
            // Update preview
            tilePreview.innerHTML = "";
            const previewImg = document.createElement("img");
            previewImg.src = e.target.result;
            tilePreview.appendChild(previewImg);

            // Create texture
            const texture = new THREE.Texture(img);
            texture.needsUpdate = true;
            texture.wrapS = THREE.RepeatWrapping;
            texture.wrapT = THREE.RepeatWrapping;
            tileTexture = texture;

            // Update materials based on application
            updateTileMaterials();

            showToast("Tile image uploaded successfully!");
          };
          img.src = e.target.result;
        };
        reader.readAsDataURL(file);
      }

      function updateTileMaterials() {
        if (!tileTexture) return;

        const tileMat = createTileMaterial(tileTexture, currentPattern);

        if (currentApplication === "floor" || currentApplication === "both") {
          if (floorMesh) {
            floorMesh.material = tileMat;
          }
        }

        if (currentApplication === "wall" || currentApplication === "both") {
          wallMeshes.forEach((wall) => {
            if (wall) {
              wall.material = tileMat;
            }
          });
        }
      }

      // Room selection
      roomOptions.forEach((option) => {
        option.addEventListener("click", () => {
          roomOptions.forEach((opt) => opt.classList.remove("active"));
          option.classList.add("active");
          currentRoom = option.dataset.room;
          createRoom(currentRoom);
          updateTileMaterials();
        });
      });

      // Application selection
      applicationOptions.forEach((option) => {
        option.addEventListener("click", () => {
          applicationOptions.forEach((opt) => opt.classList.remove("active"));
          option.classList.add("active");
          currentApplication = option.dataset.application;
          createRoom(currentRoom);
          updateTileMaterials();
        });
      });

      // Quality selection
      qualityOptions.forEach((option) => {
        option.addEventListener("click", () => {
          qualityOptions.forEach((opt) => opt.classList.remove("active"));
          option.classList.add("active");
          updateQuality(option.dataset.quality);
        });
      });

      // Pattern selection
      patternOptions.forEach((option) => {
        option.addEventListener("click", () => {
          patternOptions.forEach((opt) => opt.classList.remove("active"));
          option.classList.add("active");
          currentPattern = option.dataset.pattern;
          updateTileMaterials();
        });
      });

      // Tile size slider
      tileSizeSlider.addEventListener("input", (e) => {
        tileSizeValue.textContent = e.target.value;
        updateTileMaterials();
      });

      // Grout width slider
      groutWidthSlider.addEventListener("input", (e) => {
        groutWidthValue.textContent = e.target.value;
        updateTileMaterials();
      });

      // Grout color input
      groutColorInput.addEventListener("input", () => {
        updateTileMaterials();
      });

      // Wall height slider
      wallHeightSlider.addEventListener("input", (e) => {
        wallHeightValue.textContent = e.target.value;
        createRoom(currentRoom);
        updateTileMaterials();
      });

      // Lighting slider
      lightingSlider.addEventListener("input", (e) => {
        lightingValue.textContent = e.target.value;
        const intensity = parseFloat(e.target.value);

        // Update all lights
        scene.traverse((child) => {
          if (child instanceof THREE.Light) {
            if (child.type === "AmbientLight") {
              child.intensity = 0.3 * intensity;
            } else if (child.type === "DirectionalLight") {
              child.intensity =
                child === lights.directional
                  ? 1.0 * intensity
                  : 0.4 * intensity;
            } else if (child.type === "PointLight") {
              child.intensity *= intensity;
            } else if (child.type === "HemisphereLight") {
              child.intensity = 0.4 * intensity;
            }
          }
        });
      });

      // Reset view button
      resetViewBtn.addEventListener("click", () => {
        camera.position.set(10, 8, 10);
        camera.lookAt(0, 0, 0);
        controls.reset();
        showToast("View reset to default");
      });

      // Zoom controls
      zoomInBtn.addEventListener("click", () => {
        camera.position.multiplyScalar(0.8);
      });

      zoomOutBtn.addEventListener("click", () => {
        camera.position.multiplyScalar(1.2);
      });

      // Rotation controls
      rotateLeftBtn.addEventListener("click", () => {
        const angle = Math.PI / 8;
        const x = camera.position.x;
        const z = camera.position.z;
        camera.position.x = x * Math.cos(angle) - z * Math.sin(angle);
        camera.position.z = x * Math.sin(angle) + z * Math.cos(angle);
        camera.lookAt(0, 0, 0);
      });

      rotateRightBtn.addEventListener("click", () => {
        const angle = -Math.PI / 8;
        const x = camera.position.x;
        const z = camera.position.z;
        camera.position.x = x * Math.cos(angle) - z * Math.sin(angle);
        camera.position.z = x * Math.sin(angle) + z * Math.cos(angle);
        camera.lookAt(0, 0, 0);
      });

      // Fullscreen toggle
      toggleFullscreenBtn.addEventListener("click", () => {
        if (!isFullscreen) {
          if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
          } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
          } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
          }
          isFullscreen = true;
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
          isFullscreen = false;
        }
      });

      // Toast notification
      function showToast(message) {
        toast.textContent = message;
        toast.classList.add("show");

        setTimeout(() => {
          toast.classList.remove("show");
        }, 3000);
      }

      // Initialize the application
      init();
    </script>
  </body>
</html>
